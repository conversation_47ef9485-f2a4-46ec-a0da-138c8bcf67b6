// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum UserRole {
  PUBLISHER
  ADVERTISER
  ADMIN
}

enum CampaignStatus {
  DRAFT
  PENDING
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}

enum AdFormat {
  BANNER
  VIDEO
  NATIVE
  POPUP
}

enum PricingModel {
  CPC
  CPM
  CPA
}

model Account {
  id                String  @id @default(auto()) @map("_id") @db.ObjectId
  userId            String  @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.String
  access_token      String? @db.String
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.String
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole?
  isOnboarded   Boolean   @default(false)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts         Account[]
  sessions         Session[]
  publisherProfile PublisherProfile?
  advertiserProfile AdvertiserProfile?
}

model PublisherProfile {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @unique @db.ObjectId
  websiteUrl  String
  websiteName String
  description String?
  category    String
  monthlyTraffic Int
  region      String
  apiKey      String   @unique
  apiSecret   String
  isActive    Boolean  @default(true)
  balance     Float    @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  adSpaces AdSpace[]
}

model AdvertiserProfile {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @unique @db.ObjectId
  companyName String
  website     String?
  description String?
  industry    String
  budget      Float    @default(0)
  balance     Float    @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  campaigns Campaign[]
  paymentOrders PaymentOrder[]
  paymentTransactions PaymentTransaction[]
  walletTransactions WalletTransaction[]
}

model AdSpace {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  publisherId String   @db.ObjectId
  name        String
  format      AdFormat
  width       Int
  height      Int
  position    String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  publisher PublisherProfile @relation(fields: [publisherId], references: [id], onDelete: Cascade)
  adPlacements AdPlacement[]
}

model Campaign {
  id           String         @id @default(auto()) @map("_id") @db.ObjectId
  advertiserId String         @db.ObjectId
  name         String
  description  String?
  budget       Float
  dailyBudget  Float?
  bidAmount    Float
  pricingModel PricingModel
  targetRegions String[]
  targetCategories String[]
  status       CampaignStatus @default(DRAFT)
  startDate    DateTime?
  endDate      DateTime?
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt

  advertiser AdvertiserProfile @relation(fields: [advertiserId], references: [id], onDelete: Cascade)
  ads Ad[]
  adPlacements AdPlacement[]
  walletTransactions WalletTransaction[]
}

model Ad {
  id         String    @id @default(auto()) @map("_id") @db.ObjectId
  campaignId String    @db.ObjectId
  title      String
  description String?
  imageUrl   String?
  videoUrl   String?
  clickUrl   String
  format     AdFormat
  width      Int
  height     Int
  isActive   Boolean   @default(true)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  campaign Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  adPlacements AdPlacement[]
}

model AdPlacement {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  campaignId  String   @db.ObjectId
  adId        String   @db.ObjectId
  adSpaceId   String   @db.ObjectId
  isActive    Boolean  @default(true)
  impressions Int      @default(0)
  clicks      Int      @default(0)
  conversions Int      @default(0)
  revenue     Float    @default(0)
  cost        Float    @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  campaign Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  ad       Ad       @relation(fields: [adId], references: [id], onDelete: Cascade)
  adSpace  AdSpace  @relation(fields: [adSpaceId], references: [id], onDelete: Cascade)
}

model PaymentOrder {
  id               String   @id @default(auto()) @map("_id") @db.ObjectId
  advertiserId     String   @db.ObjectId
  amount           Float
  currency         String   @default("INR")
  razorpayOrderId  String   @unique
  status           String   @default("CREATED") // CREATED, PAID, FAILED, CANCELLED
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  advertiser AdvertiserProfile @relation(fields: [advertiserId], references: [id], onDelete: Cascade)
  transactions PaymentTransaction[]
  walletTransactions WalletTransaction[]
}

model PaymentTransaction {
  id                  String   @id @default(auto()) @map("_id") @db.ObjectId
  advertiserId        String   @db.ObjectId
  orderId             String   @db.ObjectId
  razorpayPaymentId   String?
  razorpaySignature   String?
  amount              Float
  currency            String   @default("INR")
  status              String   @default("PENDING") // PENDING, SUCCESS, FAILED
  failureReason       String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  advertiser AdvertiserProfile @relation(fields: [advertiserId], references: [id], onDelete: Cascade)
  order      PaymentOrder      @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

model WalletTransaction {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  advertiserId      String   @db.ObjectId
  type              String   // CREDIT, DEBIT
  amount            Float
  description       String
  balanceAfter      Float
  relatedOrderId    String?  @db.ObjectId
  relatedCampaignId String?  @db.ObjectId
  createdAt         DateTime @default(now())

  advertiser      AdvertiserProfile @relation(fields: [advertiserId], references: [id], onDelete: Cascade)
  relatedOrder    PaymentOrder?     @relation(fields: [relatedOrderId], references: [id], onDelete: SetNull)
  relatedCampaign Campaign?         @relation(fields: [relatedCampaignId], references: [id], onDelete: SetNull)
}

model VerificationToken {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}
