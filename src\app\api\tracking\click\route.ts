import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { headers } from "next/headers"
import { fraudPrevention } from "@/lib/fraud-prevention"
import { clickTrackingLimiter, getClientIP } from "@/lib/rate-limiter"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const trackingId = searchParams.get("id")
    const redirectUrl = searchParams.get("url")

    if (!trackingId) {
      return NextResponse.json(
        { error: "Missing tracking ID" },
        { status: 400 }
      )
    }

    // Parse tracking ID to extract campaign, ad, and ad space info
    const trackingData = parseTrackingId(trackingId)
    if (!trackingData) {
      return NextResponse.json(
        { error: "Invalid tracking ID" },
        { status: 400 }
      )
    }

    // Get client information
    const headersList = await headers()
    const ipAddress = getClientIP(request)
    const userAgent = headersList.get("user-agent") || ""
    const referrer = headersList.get("referer") || ""

    // Rate limiting check
    const rateLimit = await clickTrackingLimiter.checkLimit(ipAddress)
    if (!rateLimit.allowed) {
      console.log(`Click rate limit exceeded for IP: ${ipAddress}`)
      return NextResponse.redirect("/", { status: 302 })
    }

    // Record the click
    const clickUrl = await recordClick({
      campaignId: trackingData.campaignId,
      adId: trackingData.adId,
      adSpaceId: trackingData.adSpaceId,
      ipAddress,
      userAgent,
      referrer,
      timestamp: new Date(),
    })

    // Redirect to the actual destination URL
    const finalUrl = redirectUrl || clickUrl || "/"
    
    return NextResponse.redirect(finalUrl, {
      status: 302,
      headers: {
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0",
      },
    })
  } catch (error) {
    console.error("Click tracking error:", error)
    
    // Redirect to homepage if tracking fails
    return NextResponse.redirect("/", { status: 302 })
  }
}

export async function POST(request: NextRequest) {
  // Support POST requests for click tracking (for AJAX calls)
  try {
    const body = await request.json()
    const { trackingId, metadata } = body

    if (!trackingId) {
      return NextResponse.json(
        { error: "Missing tracking ID" },
        { status: 400 }
      )
    }

    const trackingData = parseTrackingId(trackingId)
    if (!trackingData) {
      return NextResponse.json(
        { error: "Invalid tracking ID" },
        { status: 400 }
      )
    }

    const headersList = await headers()
    const ipAddress = getClientIP(request)
    const userAgent = headersList.get("user-agent") || ""
    const referrer = headersList.get("referer") || ""

    // Rate limiting check
    const rateLimit = await clickTrackingLimiter.checkLimit(ipAddress)
    if (!rateLimit.allowed) {
      return NextResponse.json(
        { error: "Rate limit exceeded" },
        { status: 429 }
      )
    }

    const clickUrl = await recordClick({
      campaignId: trackingData.campaignId,
      adId: trackingData.adId,
      adSpaceId: trackingData.adSpaceId,
      ipAddress,
      userAgent,
      referrer,
      timestamp: new Date(),
      metadata,
    })

    return NextResponse.json({ 
      success: true,
      clickUrl 
    })
  } catch (error) {
    console.error("Click tracking error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

interface TrackingData {
  campaignId: string
  adId: string
  adSpaceId: string
  timestamp: string
}

function parseTrackingId(trackingId: string): TrackingData | null {
  try {
    // Handle fallback tracking
    if (trackingId === "fallback") {
      return {
        campaignId: "fallback",
        adId: "fallback",
        adSpaceId: "fallback",
        timestamp: Date.now().toString(),
      }
    }

    // Parse format: campaignId_adId_adSpaceId_timestamp
    const parts = trackingId.split("_")
    if (parts.length !== 4) {
      return null
    }

    return {
      campaignId: parts[0],
      adId: parts[1],
      adSpaceId: parts[2],
      timestamp: parts[3],
    }
  } catch (error) {
    console.error("Failed to parse tracking ID:", error)
    return null
  }
}

interface ClickData {
  campaignId: string
  adId: string
  adSpaceId: string
  ipAddress: string
  userAgent: string
  referrer: string
  timestamp: Date
  metadata?: any
}

async function recordClick(data: ClickData): Promise<string> {
  try {
    // Handle fallback clicks
    if (data.campaignId === "fallback") {
      console.log("Fallback click recorded")
      return `${process.env.NEXT_PUBLIC_APP_URL}/auth/signup?role=advertiser`
    }

    // Fraud prevention check
    const fraudCheck = await fraudPrevention.validateClick({
      adId: data.adId,
      adSpaceId: data.adSpaceId,
      campaignId: data.campaignId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      timestamp: data.timestamp,
    })

    if (!fraudCheck.isValid) {
      console.log(`Fraudulent click detected: ${fraudCheck.reason}`, {
        adId: data.adId,
        ipAddress: data.ipAddress,
        riskScore: fraudCheck.riskScore
      })
      // Still return the URL but don't record the click
      const ad = await prisma.ad.findUnique({
        where: { id: data.adId },
      })
      return ad?.clickUrl || "/"
    }

    // Get the ad to find the click URL
    const ad = await prisma.ad.findUnique({
      where: { id: data.adId },
    })

    if (!ad) {
      console.error("Ad not found for click tracking:", data.adId)
      return "/"
    }

    // Find or create ad placement record
    let adPlacement = await prisma.adPlacement.findFirst({
      where: {
        campaignId: data.campaignId,
        adId: data.adId,
        adSpaceId: data.adSpaceId,
      },
    })

    if (!adPlacement) {
      // Create new ad placement if it doesn't exist
      adPlacement = await prisma.adPlacement.create({
        data: {
          campaignId: data.campaignId,
          adId: data.adId,
          adSpaceId: data.adSpaceId,
          isActive: true,
          impressions: 0,
          clicks: 1,
          conversions: 0,
          revenue: 0,
          cost: 0,
        },
      })
    } else {
      // Update existing ad placement
      await prisma.adPlacement.update({
        where: { id: adPlacement.id },
        data: {
          clicks: { increment: 1 },
        },
      })
    }

    // Update costs and revenue for CPC campaigns
    const campaign = await prisma.campaign.findUnique({
      where: { id: data.campaignId },
    })

    if (campaign && campaign.pricingModel === "CPC") {
      const cost = campaign.bidAmount
      const publisherRevenue = cost * 0.7 // 70% revenue share to publisher

      await prisma.adPlacement.update({
        where: { id: adPlacement.id },
        data: {
          revenue: { increment: publisherRevenue },
          cost: { increment: cost },
        },
      })

      // Update publisher balance
      const adSpace = await prisma.adSpace.findUnique({
        where: { id: data.adSpaceId },
        include: { publisher: true },
      })

      if (adSpace) {
        await prisma.publisherProfile.update({
          where: { id: adSpace.publisherId },
          data: {
            balance: { increment: publisherRevenue },
          },
        })
      }

      // Update advertiser spending
      await prisma.advertiserProfile.update({
        where: { id: campaign.advertiserId },
        data: {
          balance: { decrement: cost },
        },
      })

      // Broadcast real-time update
      try {
        const { broadcastUpdate } = await import("@/app/api/realtime/dashboard/route")
        broadcastUpdate({
          type: "click",
          campaignId: data.campaignId,
          adSpaceId: data.adSpaceId,
          cost,
          revenue: publisherRevenue,
          pricingModel: "CPC"
        })
      } catch (error) {
        console.error("Error broadcasting click update:", error)
      }
    }

    // Log for analytics
    console.log("Click recorded:", {
      campaignId: data.campaignId,
      adId: data.adId,
      adSpaceId: data.adSpaceId,
      clickUrl: ad.clickUrl,
      timestamp: data.timestamp,
    })

    // In a real implementation, you might also:
    // 1. Store detailed click data in a separate analytics table
    // 2. Send to a real-time analytics service
    // 3. Update campaign performance metrics
    // 4. Trigger conversion tracking
    // 5. Update bidding algorithm performance data
    // 6. Check for click fraud

    return ad.clickUrl
  } catch (error) {
    console.error("Failed to record click:", error)
    return "/"
  }
}
